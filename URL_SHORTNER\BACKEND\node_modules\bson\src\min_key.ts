import { BSONValue } from './bson_value';

/** @public */
export interface MinKeyExtended {
  $minKey: 1;
}

/**
 * A class representation of the BSON MinKey type.
 * @public
 * @category BSONType
 */
export class <PERSON><PERSON><PERSON> extends BSONValue {
  get _bsontype(): '<PERSON><PERSON><PERSON>' {
    return 'MinKey';
  }

  /** @internal */
  toExtendedJSON(): MinKeyExtended {
    return { $minKey: 1 };
  }

  /** @internal */
  static fromExtendedJSON(): Min<PERSON><PERSON> {
    return new MinKey();
  }

  inspect(): string {
    return 'new <PERSON><PERSON>ey()';
  }
}
